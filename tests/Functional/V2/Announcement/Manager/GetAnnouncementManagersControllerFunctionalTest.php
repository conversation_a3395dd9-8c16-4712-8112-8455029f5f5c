<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Announcement\Manager;

use App\Entity\Announcement;
use App\Entity\AnnouncementManager;
use App\Entity\Course;
use App\Entity\Setting;
use App\Entity\SettingGroup;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\AnnouncementHelperTrait;
use App\Tests\Functional\HelperTrait\CourseHelperTrait;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementEndpoints;
use App\Tests\Functional\HelperTrait\SettingHelperTrait;
use App\Tests\Functional\V2\Fixtures\AnnouncementManagerFixtureTrait;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Symfony\Component\HttpFoundation\Response;

class GetAnnouncementManagersControllerFunctionalTest extends FunctionalTestCase
{
    public const string MANAGER_SHARED_ANNOUNCEMENT_SETTING = 'app.announcement.managers.sharing';

    use AnnouncementManagerFixtureTrait;
    use SettingHelperTrait;

    private array $usersIds = [];

    private ?Setting $originalSetting = null;

    public function testBadRequest(): void
    {
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::getAnnouncementManagersEndpoint(announcementId: -1),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);

        $this->assertArrayHasKey('metadata', $content);
        $this->assertEquals([
            'violations' => ['[id]' => 'id must be greater than 0.'],
        ], $content['metadata']);
    }

    public  function testAnnouncementNotFound():void
    {
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::getAnnouncementManagersEndpoint(announcementId: 9999),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Announcement not found', $content['message']);
    }

    public  function testForbiddenRequest(): void {
        $setting = $this->getEntityManager()
            ->getRepository(Setting::class)
            ->findOneBy(['code' => self::MANAGER_SHARED_ANNOUNCEMENT_SETTING]);

        if ($setting) {
            $this->originalSetting = $setting;
            $this->getEntityManager()->remove($setting);
            $this->getEntityManager()->flush();
        }

        $this->createAndGetSetting(
            code: self::MANAGER_SHARED_ANNOUNCEMENT_SETTING,
           value: 'true',
            settingGroup: $this->getEntityManager()
                ->getRepository(SettingGroup::class)
                ->find(3) // Assuming group ID 3 is the correct one
        );



        $course = $this->createAndGetCourse();
        $announcement = $this->createAndGetAnnouncement(course: $course);

        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::getAnnouncementManagersEndpoint(announcementId: $announcement->getId()),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());


    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testGetAnnouncementManagers(): void
    {
        $user1 = $this->createAndGetUser(
            firstName: 'Manager',
            lastName: 'One',
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>'
        );
        $this->usersIds[] = $user1->getId();

        $user2 = $this->createAndGetUser(
            firstName: 'Manager',
            lastName: 'Two',
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>'
        );

        $this->usersIds[] = $user2->getId();


        $course = $this->createAndGetCourse();
        $announcement = $this->createAndGetAnnouncement(course: $course);

        $this->setAndGetAnnouncementManger(
            userId:  $user1->getId(),
            announcementId: $announcement->getId()
        );

        $this->setAndGetAnnouncementManger(
            userId:  $user2->getId(),
            announcementId: $announcement->getId()
        );

        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::getAnnouncementManagersEndpoint(announcementId: $announcement->getId()),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $this->assertCount(2, $content['data']);
        $data = $content['data'];
        $this->assertIsArray($data);

        $this->assertEquals([
            [
                'id' => $user1->getId(),
                'email' => $user1->getEmail(),
                'name' => $user1->getFirstName(),
                'lastName' => $user1->getLastName(),
            ],
            [
                'id' => $user2->getId(),
                'email' => $user2->getEmail(),
                'name' => $user2->getFirstName(),
                'lastName' => $user2->getLastName(),
            ],
        ], $data);
    }

    private function restoreOriginalSetting(): void
    {
        if ($this->originalSetting) {
            // Restore the original setting
            $currentSetting = $this->getEntityManager()
                ->getRepository(Setting::class)
                ->findOneBy(['code' => self::MANAGER_SHARED_ANNOUNCEMENT_SETTING]);

            if ($currentSetting) {
                $currentSetting->setValue($this->originalSetting->getValue());
                $this->getEntityManager()->persist($currentSetting);
                $this->getEntityManager()->flush();
            }
        }
    }

    protected function tearDown(): void
    {
        // Restore original setting state if it was modified
        $this->restoreOriginalSetting();

        $this->truncateEntities(
            [
                AnnouncementManager::class,
                Announcement::class,
                Course::class,
            ]
        );

        $this->hardDeleteUsersByIds($this->usersIds);

        parent::tearDown();
    }





}
