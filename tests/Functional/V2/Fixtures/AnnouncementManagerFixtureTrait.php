<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Fixtures;

use App\Tests\V2\Mother\Announcement\Manager\AnnouncementManagerMother;
use App\V2\Domain\Announcement\Manager\AnnouncementManager;


trait AnnouncementManagerFixtureTrait
{
    /**
     * Interacting with the InMemory repository.
     */
    private function setAndGetAnnouncementManger(
        ?int $userId = null,
        ?int $announcementId = null,
    ): AnnouncementManager
    {
        $manager = AnnouncementManagerMother::create(
            userId: $userId,
            announcementId:  $announcementId,
        );

        $this->client->getContainer()->get('App\V2\Domain\Announcement\Manager\AnnouncementManagerRepository')->insert($manager);

        return $manager;
    }
}
